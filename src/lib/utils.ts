import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const convertNumberToWords = (num: number): string => {
  if (num === 0) return "Zero Rupees only";

  const ones = [
    "",
    "One",
    "Two",
    "Three",
    "Four",
    "Five",
    "Six",
    "Seven",
    "Eight",
    "Nine",
    "Ten",
    "Eleven",
    "Twelve",
    "Thirteen",
    "Fourteen",
    "Fifteen",
    "Sixteen",
    "Seventeen",
    "Eighteen",
    "Nineteen",
  ];

  const tens = [
    "",
    "",
    "Twenty",
    "Thirty",
    "Forty",
    "Fifty",
    "Sixty",
    "Seventy",
    "Eighty",
    "Ninety",
  ];

  const convertHundreds = (n: number): string => {
    let result = "";

    if (n >= 100) {
      result += ones[Math.floor(n / 100)] + " Hundred ";
      n %= 100;
    }

    if (n >= 20) {
      result += tens[Math.floor(n / 10)] + " ";
      n %= 10;
    }

    if (n > 0) {
      result += ones[n] + " ";
    }

    return result;
  };

  let result = "";
  const crores = Math.floor(num / 10000000);
  num %= 10000000;

  const lakhs = Math.floor(num / 100000);
  num %= 100000;

  const thousands = Math.floor(num / 1000);
  num %= 1000;

  const hundreds = num;

  if (crores > 0) {
    result += convertHundreds(crores) + "Crore ";
  }

  if (lakhs > 0) {
    result += convertHundreds(lakhs) + "Lakh ";
  }

  if (thousands > 0) {
    result += convertHundreds(thousands) + "Thousand ";
  }

  if (hundreds > 0) {
    result += convertHundreds(hundreds);
  }

  return result.trim() + " Rupees only";
};

/**
 * Generates and downloads a PDF from an HTML element
 * @param elementId - The ID of the HTML element to convert to PDF
 * @param filename - The name of the PDF file to download
 */
export const generatePDF = async (
  elementId: string,
  filename: string
): Promise<void> => {
  try {
    // Import the libraries dynamically to avoid SSR issues
    const html2canvas = (await import("html2canvas")).default;
    const jsPDF = (await import("jspdf")).default;

    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // Create canvas from the element with exact styling preservation
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: element.scrollWidth,
      height: element.scrollHeight,
      logging: false,
      removeContainer: true,
      ignoreElements: (element) => {
        // Only hide elements with print:hidden class
        return element.classList?.contains("print:hidden") || false;
      },
    });

    const imgData = canvas.toDataURL("image/png", 1.0);

    // Standard invoice paper size (US Letter: 8.5" x 11")
    const pageWidth = 215.9; // 8.5 inches in mm
    const pageHeight = 279.4; // 11 inches in mm

    // Calculate image dimensions to fit the page with margins
    const margin = 10; // 10mm margin on all sides
    const maxWidth = pageWidth - margin * 2;
    const maxHeight = pageHeight - margin * 2;

    const imgWidth = maxWidth;
    const imgHeight = (canvas.height * maxWidth) / canvas.width;

    // Create PDF with US Letter size
    const pdf = new jsPDF("p", "mm", [pageWidth, pageHeight]);

    const yPosition = margin;
    const remainingHeight = imgHeight;

    // Add the image to PDF (single page for now, will handle multi-page if needed)
    if (imgHeight <= maxHeight) {
      // Single page
      pdf.addImage(imgData, "PNG", margin, margin, imgWidth, imgHeight);
    } else {
      // Multi-page handling
      let yPosition = 0;
      let remainingHeight = imgHeight;
      let pageCount = 0;

      while (remainingHeight > 0) {
        if (pageCount > 0) {
          pdf.addPage();
        }

        const currentPageHeight = Math.min(remainingHeight, maxHeight);

        // Create a cropped version of the image for this page
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");
        tempCanvas.width = canvas.width;
        tempCanvas.height = (currentPageHeight / imgHeight) * canvas.height;

        const img = new Image();
        img.onload = () => {
          tempCtx?.drawImage(
            img,
            0,
            yPosition * (canvas.height / imgHeight),
            canvas.width,
            tempCanvas.height,
            0,
            0,
            canvas.width,
            tempCanvas.height
          );
        };
        img.src = imgData;

        pdf.addImage(
          imgData,
          "PNG",
          margin,
          margin,
          imgWidth,
          currentPageHeight
        );

        yPosition += currentPageHeight;
        remainingHeight -= currentPageHeight;
        pageCount++;
      }
    }

    // Download the PDF
    pdf.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  }
};
