import { Button } from "@/components/ui/button";
import type { Invoice } from "@/lib/types";
import { convertNumberToWords } from "@/lib/utils";
import { ArrowLeft, Download } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface InvoicePreviewProps {
  invoice: Invoice;
  onBack: () => void;
}

const InvoicePreview: React.FC<InvoicePreviewProps> = ({ invoice, onBack }) => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const handleDownload = async () => {
    setIsGeneratingPDF(true);
    try {
      const { generateInvoicePDF } = await import("@/components/invoice-pdf");
      const filename = `Invoice_${invoice.invoice_number}_${
        invoice.customer?.name?.replace(/\s+/g, "_") || "Customer"
      }.pdf`;
      await generateInvoicePDF(invoice, filename);
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF. Please try again.");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center print:hidden">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Form
        </Button>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleDownload}
            disabled={isGeneratingPDF}
          >
            <Download className="mr-2 h-4 w-4" />
            {isGeneratingPDF ? "Generating PDF..." : "Download PDF"}
          </Button>
        </div>
      </div>

      {/* Invoice Template */}
      <div
        id="invoice-content"
        className="bg-white p-8 shadow-lg rounded-lg print:shadow-none print:rounded-none min-h-[297mm] flex flex-col print:min-h-[297mm]"
      >
        {/* Main Content */}
        <div className="flex-1">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-4">Invoice</h1>
          </div>

          {/* Company Info */}
          <div className="border border-gray-400 mb-6">
            <div className="bg-gray-100 p-4 border-b border-gray-400">
              <h2 className="text-xl font-bold">EXPRESS PRINT</h2>
              <p className="text-sm">
                Panisheola Sitalatala, Haripal, Hooghly, Pin-712405
              </p>
              <p className="text-sm">Phone: 9832407944</p>
            </div>

            {/* Bill To and Invoice Details */}
            <div className="grid grid-cols-2">
              <div className="p-4 border-r border-gray-400">
                <h3 className="font-semibold mb-2">Bill To:</h3>
                <p className="font-medium">{invoice.customer!.name}</p>
                <p className="text-sm">{invoice.customer!.phone}</p>
                {invoice.customer!.address && (
                  <p className="text-sm">{invoice.customer!.address}</p>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold mb-2">Invoice Details:</h3>
                <p className="text-sm">No: {invoice.invoice_number}</p>
                <p className="text-sm">
                  Date: {new Date(invoice.date).toLocaleDateString("en-GB")}
                </p>
              </div>
            </div>
          </div>

          {/* Items Table */}
          <table className="w-full border border-gray-400 mb-6">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-400 p-2 text-left">S.No.</th>
                <th className="border border-gray-400 p-2 text-left">
                  Item Name
                </th>
                <th className="border border-gray-400 p-2 text-center">
                  Quantity
                </th>
                <th className="border border-gray-400 p-2 text-center">Unit</th>
                <th className="border border-gray-400 p-2 text-right">
                  Price/piece (₹)
                </th>
                <th className="border border-gray-400 p-2 text-right">
                  Amount(₹)
                </th>
              </tr>
            </thead>
            <tbody>
              {invoice.items!.map((item, index) => (
                <tr key={item.id}>
                  <td className="border border-gray-400 p-2">{index + 1}</td>
                  <td className="border border-gray-400 p-2">{item.name}</td>
                  <td className="border border-gray-400 p-2 text-center">
                    {item.quantity}
                  </td>
                  <td className="border border-gray-400 p-2 text-center">
                    {item.unit}
                  </td>
                  <td className="border border-gray-400 p-2 text-right">
                    ₹ {item.price.toFixed(2)}
                  </td>
                  <td className="border border-gray-400 p-2 text-right">
                    ₹ {item.amount.toFixed(2)}
                  </td>
                </tr>
              ))}
              <tr>
                <td
                  colSpan={4}
                  className="border border-gray-400 p-2 font-semibold"
                >
                  Total
                </td>
                <td className="border border-gray-400 p-2 text-right font-semibold">
                  {invoice.items!.reduce((sum, item) => sum + item.quantity, 0)}
                </td>
                <td className="border border-gray-400 p-2 text-right font-semibold">
                  ₹ {invoice.subtotal.toFixed(2)}
                </td>
              </tr>
            </tbody>
          </table>

          {/* Notes */}
          {invoice.notes && (
            <div className="mt-4 mb-6">
              <h4 className="font-semibold text-sm">Notes:</h4>
              <p className="text-sm text-gray-600">{invoice.notes}</p>
            </div>
          )}
        </div>

        {/* Bottom Section - Totals and Signature */}
        <div className="mt-auto">
          {/* Totals */}
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div></div>
            <div className="border border-gray-400">
              <div className="grid grid-cols-2 text-sm">
                <div className="p-2 border-b border-gray-400">Sub Total</div>
                <div className="p-2 border-b border-gray-400 text-right">
                  : ₹ {invoice.subtotal.toFixed(2)}
                </div>

                <div className="p-2 border-b border-gray-400">
                  Discount ({invoice.discount_percentage}%)
                </div>
                <div className="p-2 border-b border-gray-400 text-right">
                  : ₹ {invoice.discount.toFixed(2)}
                </div>

                <div className="p-2 font-semibold">Total</div>
                <div className="p-2 text-right font-semibold">
                  : ₹ {invoice.total.toFixed(2)}
                </div>
              </div>

              <div className="p-2 border-t border-gray-400">
                <div className="text-sm font-semibold">
                  Invoice Amount In Words :
                </div>
                <div className="text-sm mt-1">
                  {convertNumberToWords(invoice.total)}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mb-6 grid grid-cols-2 gap-6">
            <div></div>
            <div className="p-4 text-right border border-gray-400">
              <p className="font-semibold">For EXPRESS PRINT:</p>
              <div className="mt-12 mb-4">
                <p className="text-sm">Authorized Signatory</p>
              </div>
            </div>
          </div>

          {/* Footer branding */}
          <div className="text-center text-xs text-gray-500 mt-8 print:hidden">
            Generated by Express Print Invoice System
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoicePreview;
