import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
} from '@react-pdf/renderer';
import type { Invoice } from '@/lib/types';
import { convertNumberToWords } from '@/lib/utils';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontSize: 10,
    fontFamily: 'Helvetica',
  },
  header: {
    textAlign: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  companySection: {
    border: '1px solid #666666',
    marginBottom: 20,
  },
  companyHeader: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderBottom: '1px solid #666666',
  },
  companyName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  companyInfo: {
    fontSize: 10,
    marginBottom: 2,
  },
  billToSection: {
    flexDirection: 'row',
  },
  billToLeft: {
    flex: 1,
    padding: 12,
    borderRight: '1px solid #666666',
  },
  billToRight: {
    flex: 1,
    padding: 12,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  customerName: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  customerInfo: {
    fontSize: 10,
    marginBottom: 2,
  },
  invoiceDetails: {
    fontSize: 10,
    marginBottom: 2,
  },
  itemsTable: {
    marginTop: 20,
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderTop: '1px solid #666666',
    borderLeft: '1px solid #666666',
    borderRight: '1px solid #666666',
    borderBottom: '1px solid #666666',
    padding: 8,
  },
  tableRow: {
    flexDirection: 'row',
    borderLeft: '1px solid #666666',
    borderRight: '1px solid #666666',
    borderBottom: '1px solid #666666',
    padding: 8,
  },
  tableCell: {
    fontSize: 10,
    textAlign: 'left',
  },
  tableCellCenter: {
    fontSize: 10,
    textAlign: 'center',
  },
  tableCellRight: {
    fontSize: 10,
    textAlign: 'right',
  },
  slNo: { width: '8%' },
  itemName: { width: '40%' },
  quantity: { width: '12%' },
  unit: { width: '10%' },
  price: { width: '15%' },
  amount: { width: '15%' },
  totalsSection: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  totalsLeft: {
    flex: 1,
  },
  totalsRight: {
    flex: 1,
    border: '1px solid #666666',
  },
  totalRow: {
    flexDirection: 'row',
    borderBottom: '1px solid #666666',
  },
  totalLabel: {
    flex: 1,
    padding: 8,
    fontSize: 10,
  },
  totalValue: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    textAlign: 'right',
  },
  totalRowFinal: {
    flexDirection: 'row',
  },
  totalLabelFinal: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    fontWeight: 'bold',
  },
  totalValueFinal: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  amountInWords: {
    padding: 8,
    borderTop: '1px solid #666666',
  },
  amountInWordsTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  amountInWordsText: {
    fontSize: 10,
  },
  footerSection: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  footerLeft: {
    flex: 1,
  },
  footerRight: {
    flex: 1,
    border: '1px solid #666666',
    padding: 12,
    textAlign: 'right',
  },
  signatureTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 40,
  },
  signatureText: {
    fontSize: 10,
  },
  notesSection: {
    marginTop: 15,
  },
  notesTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 10,
    color: '#666666',
  },
});

interface InvoicePDFProps {
  invoice: Invoice;
}

const InvoicePDF: React.FC<InvoicePDFProps> = ({ invoice }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Invoice</Text>
      </View>

      {/* Company Info */}
      <View style={styles.companySection}>
        <View style={styles.companyHeader}>
          <Text style={styles.companyName}>EXPRESS PRINT</Text>
          <Text style={styles.companyInfo}>
            Panisheola Sitalatala, Haripal, Hooghly, Pin-712405
          </Text>
          <Text style={styles.companyInfo}>Phone: 9832407944</Text>
        </View>

        {/* Bill To and Invoice Details */}
        <View style={styles.billToSection}>
          <View style={styles.billToLeft}>
            <Text style={styles.sectionTitle}>Bill To:</Text>
            <Text style={styles.customerName}>{invoice.customer?.name}</Text>
            <Text style={styles.customerInfo}>{invoice.customer?.phone}</Text>
            {invoice.customer?.address && (
              <Text style={styles.customerInfo}>{invoice.customer.address}</Text>
            )}
          </View>
          <View style={styles.billToRight}>
            <Text style={styles.sectionTitle}>Invoice Details:</Text>
            <Text style={styles.invoiceDetails}>No: {invoice.invoice_number}</Text>
            <Text style={styles.invoiceDetails}>
              Date: {new Date(invoice.date).toLocaleDateString('en-GB')}
            </Text>
          </View>
        </View>
      </View>

      {/* Items Table */}
      <View style={styles.itemsTable}>
        {/* Table Header */}
        <View style={styles.tableHeader}>
          <Text style={[styles.tableCell, styles.slNo]}>Sl. No.</Text>
          <Text style={[styles.tableCell, styles.itemName]}>Item Name</Text>
          <Text style={[styles.tableCellCenter, styles.quantity]}>Qty</Text>
          <Text style={[styles.tableCellCenter, styles.unit]}>Unit</Text>
          <Text style={[styles.tableCellRight, styles.price]}>Price (₹)</Text>
          <Text style={[styles.tableCellRight, styles.amount]}>Amount (₹)</Text>
        </View>

        {/* Table Rows */}
        {invoice.items?.map((item, index) => (
          <View key={item.id} style={styles.tableRow}>
            <Text style={[styles.tableCellCenter, styles.slNo]}>{index + 1}</Text>
            <Text style={[styles.tableCell, styles.itemName]}>{item.name}</Text>
            <Text style={[styles.tableCellCenter, styles.quantity]}>{item.quantity}</Text>
            <Text style={[styles.tableCellCenter, styles.unit]}>{item.unit}</Text>
            <Text style={[styles.tableCellRight, styles.price]}>{item.price.toFixed(2)}</Text>
            <Text style={[styles.tableCellRight, styles.amount]}>{item.amount.toFixed(2)}</Text>
          </View>
        ))}
      </View>

      {/* Totals */}
      <View style={styles.totalsSection}>
        <View style={styles.totalsLeft}></View>
        <View style={styles.totalsRight}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Sub Total</Text>
            <Text style={styles.totalValue}>: ₹ {invoice.subtotal.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Discount ({invoice.discount_percentage}%)</Text>
            <Text style={styles.totalValue}>: ₹ {invoice.discount.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRowFinal}>
            <Text style={styles.totalLabelFinal}>Total</Text>
            <Text style={styles.totalValueFinal}>: ₹ {invoice.total.toFixed(2)}</Text>
          </View>
          <View style={styles.amountInWords}>
            <Text style={styles.amountInWordsTitle}>Invoice Amount In Words :</Text>
            <Text style={styles.amountInWordsText}>
              {convertNumberToWords(invoice.total)}
            </Text>
          </View>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footerSection}>
        <View style={styles.footerLeft}></View>
        <View style={styles.footerRight}>
          <Text style={styles.signatureTitle}>For EXPRESS PRINT:</Text>
          <Text style={styles.signatureText}>Authorized Signatory</Text>
        </View>
      </View>

      {/* Notes */}
      {invoice.notes && (
        <View style={styles.notesSection}>
          <Text style={styles.notesTitle}>Notes:</Text>
          <Text style={styles.notesText}>{invoice.notes}</Text>
        </View>
      )}
    </Page>
  </Document>
);

export default InvoicePDF;

// Function to generate and download PDF
export const generateInvoicePDF = async (invoice: Invoice, filename: string): Promise<void> => {
  try {
    const blob = await pdf(<InvoicePDF invoice={invoice} />).toBlob();

    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF. Please try again.');
  }
};
