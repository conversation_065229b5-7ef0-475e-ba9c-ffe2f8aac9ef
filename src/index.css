@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font: "Geist, sans-serif";
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.09 0.015 285.75);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.09 0.015 285.75);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.09 0.015 285.75);
  --primary: oklch(0.18 0.014 285.75);
  --primary-foreground: oklch(0.98 0.013 285.75);
  --secondary: oklch(0.96 0.006 285.75);
  --secondary-foreground: oklch(0.18 0.014 285.75);
  --muted: oklch(0.96 0.006 285.75);
  --muted-foreground: oklch(0.45 0.003 285.75);
  --accent: oklch(0.96 0.006 285.75);
  --accent-foreground: oklch(0.18 0.014 285.75);
  --destructive: oklch(0.7 0.176 17.38);
  --destructive-foreground: oklch(0.98 0.013 285.75);
  --border: oklch(0.91 0.005 285.75);
  --input: oklch(0.91 0.005 285.75);
  --ring: oklch(0.09 0.015 285.75);
  --radius: 0.5rem;
  --sidebar-background: oklch(0.98 0.002 285.75);
  --sidebar-foreground: oklch(0.27 0.003 285.75);
  --sidebar-primary: oklch(0.09 0.005 285.75);
  --sidebar-primary-foreground: oklch(0.98 0.002 285.75);
  --sidebar-accent: oklch(0.96 0.003 285.75);
  --sidebar-accent-foreground: oklch(0.09 0.005 285.75);
  --sidebar-border: oklch(0.91 0.026 285.75);
  --sidebar-ring: oklch(0.7 0.191 230.54);
}

.dark {
  --background: oklch(0.09 0.015 285.75);
  --foreground: oklch(0.98 0.013 285.75);
  --card: oklch(0.09 0.015 285.75);
  --card-foreground: oklch(0.98 0.013 285.75);
  --popover: oklch(0.09 0.015 285.75);
  --popover-foreground: oklch(0.98 0.013 285.75);
  --primary: oklch(0.98 0.013 285.75);
  --primary-foreground: oklch(0.18 0.014 285.75);
  --secondary: oklch(0.27 0.029 285.75);
  --secondary-foreground: oklch(0.98 0.013 285.75);
  --muted: oklch(0.27 0.029 285.75);
  --muted-foreground: oklch(0.65 0.015 285.75);
  --accent: oklch(0.27 0.029 285.75);
  --accent-foreground: oklch(0.98 0.013 285.75);
  --destructive: oklch(0.43 0.25 17.38);
  --destructive-foreground: oklch(0.98 0.013 285.75);
  --border: oklch(0.27 0.029 285.75);
  --input: oklch(0.27 0.029 285.75);
  --ring: oklch(0.83 0.12 285.75);
  --sidebar-background: oklch(0.09 0.005 285.75);
  --sidebar-foreground: oklch(0.96 0.003 285.75);
  --sidebar-primary: oklch(0.7 0.191 230.54);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.27 0.015 285.75);
  --sidebar-accent-foreground: oklch(0.96 0.003 285.75);
  --sidebar-border: oklch(0.27 0.015 285.75);
  --sidebar-ring: oklch(0.7 0.191 230.54);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
